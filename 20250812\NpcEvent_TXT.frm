Option Explicit

' 声明控件变量
Private WithEvents txtFilePath As MSForms.TextBox
Private WithEvents txtContentEditor As MSForms.TextBox

' 存储文件路径的变量
Private mFilePath As String

' 声明Windows API用于鼠标滚轮支持
Private Declare PtrSafe Function GetCursorPos Lib "user32" (lpPoint As POINTAPI) As Long
Private Declare PtrSafe Function WindowFromPoint Lib "user32" (ByVal xPoint As Long, ByVal yPoint As Long) As Long

Private Type POINTAPI
    x As Long
    y As Long
End Type

' 窗体初始化
Private Sub UserForm_Initialize()
    ' 设置窗体属性
    Me.Caption = "脚本编辑窗口"
    Me.Width = 670
    Me.Height = 600
   ' Me.BackColor = &HC0C0C0
    Me.BackColor = &H8000000F
    ' 创建所有控件
    CreateControls
    
    ' 如果已有文件路径，设置到文本框
    'If Len(mFilePath) > 0 Then
    '     txtFilePath.Text = mFilePath
    ' End If
    
    ' 应用示例内容
    'ApplySampleContent
End Sub


' 创建所有控件
Private Sub CreateControls()
    ' ===========================顶部文件路径区域 ========================
    ' 文件路径标签
    Dim lblPath As MSForms.Label
    Set lblPath = Me.Controls.Add("Forms.Label.1", "lblPath")
    With lblPath
        .Caption = "脚本文件路径"
        .Top = 14
        .Left = 10
        .Width = 90
        .Height = 20
        .Font.Size = 9
        .Font.Name = "微软雅黑"
    End With
    
    ' 文件路径输入框
    Set txtFilePath = Me.Controls.Add("Forms.TextBox.1", "txtFilePath")
    With txtFilePath
        .Top = 10
        .Left = 70
        .Width = 450
        .Height = 20
        .Font.Size = 9
        .BackColor = &HFFFFFF
        .Font.Name = "微软雅黑"
    End With
    
    ' 保存按钮
    Dim btnSave As MSForms.CommandButton
    Set btnSave = Me.Controls.Add("Forms.CommandButton.1", "btnSave")
    With btnSave
        .Caption = "保存..."
        .Top = 9
        .Left = 525
        .Width = 50
        .Height = 20
        .Font.Size = 9
        .BackColor = &HE0E0E0
        .Font.Name = "微软雅黑"
    End With
    
    ' 载入当前脚本按钮
    Dim btnLoad As MSForms.CommandButton
    Set btnLoad = Me.Controls.Add("Forms.CommandButton.1", "btnLoad")
    With btnLoad
        .Caption = "载入当前脚本"
        .Top = 9
        .Left = 580
        .Width = 65
        .Height = 20
        .Font.Size = 9
        .BackColor = &HE0E0E0
        .Font.Name = "微软雅黑"
    End With
    
    ' ===========================左侧脚本编写环境 ===========================
    ' 脚本编写环境框架
    Dim frameScript As MSForms.Frame
    Set frameScript = Me.Controls.Add("Forms.Frame.1", "frameScript")
    With frameScript
        .Caption = "脚本编写环境"
        .Top = 40
        .Left = 10
        .Width = 150 + 490
        .Height = 400
        .Font.Size = 9
        .BorderStyle = fmBorderStyleSingle
        .SpecialEffect = fmSpecialEffectSunken
     '   .BackColor = &HC0C0C0
        .BackColor = &HE0E0E0
        '.Font.Name = "微软雅黑"
    End With
    
    ' 脚本搜索操作窗标签
    Dim lblSearchPanel As MSForms.Label
    Set lblSearchPanel = frameScript.Controls.Add("Forms.Label.1", "lblSearchPanel")
    With lblSearchPanel
        .Caption = "脚本搜索操作窗"
        .Top = 10
        .Left = 40
        .Width = 100
        .Height = 20
        .Font.Size = 9
    End With
    
    ' 列表框控件 - 匹配图片风格
    Dim lstScriptResults As MSForms.listBox
    Set lstScriptResults = frameScript.Controls.Add("Forms.ListBox.1", "lstScriptResults")
    With lstScriptResults
        .Top = 20
        .Left = 3
        .Width = 140
        .Height = 340
        .BackColor = &HFFFFFF
        .BorderStyle = fmBorderStyleSingle
        .Font.Name = "微软雅黑"
        .Font.Size = 8
        .ColumnCount = 1
    End With
    
    ' 在列表框中添加示例项（实际应用中填充脚本列表）
    With lstScriptResults
        .AddItem "NPC_Warrior_101.scr"
        .AddItem "NPC_Merchant_205.scr"
        .AddItem "NPC_Guard_309.scr"
        .AddItem "Quest_GoblinHunt.scr"
        .AddItem "Town_Hall_Event.scr"
    End With
    
    ' 搜索文本框
    Dim txtScriptEditor As MSForms.TextBox
    Set txtScriptEditor = frameScript.Controls.Add("Forms.TextBox.1", "txtScriptEditor")
    With txtScriptEditor
        .Top = 350
        .Left = 3
        .Width = 140
        .Height = 20
        .MultiLine = True
        .ScrollBars = fmScrollBarsVertical
        .Font.Name = "微软雅黑"
        .Font.Size = 9
        .BackColor = &HFFFFFF
    End With
    
    ' 搜索按钮
    Dim btnSearch As MSForms.CommandButton
    Set btnSearch = frameScript.Controls.Add("Forms.CommandButton.1", "btnSearch")
    With btnSearch
        .Caption = "搜索"
        .Top = 372
        .Left = 3
        .Width = 50
        .Height = 18
        .Font.Size = 9
        .BackColor = &HE0E0E0
    End With
    
    ' 停止按钮
    Dim btnStop As MSForms.CommandButton
    Set btnStop = frameScript.Controls.Add("Forms.CommandButton.1", "btnStop")
    With btnStop
        .Caption = "停止"
        .Top = 372
        .Left = 60
        .Width = 50
        .Height = 18
        .Font.Size = 9
        .BackColor = &HE0E0E0
      '  .ForeColor = &HE0E0E0
    End With
    ' 页眉页脚标签
    'Dim lblHeaderFooter As MSForms.Label
    'Set lblHeaderFooter = frameScript.Controls.Add("Forms.Label.1", "lblHeaderFooter")
    'With lblHeaderFooter
    '    .Caption = "标题页眉/页脚内容"
    '    .Top = 360
    '    .Left = 10
    '    .Width = 120
    '    .Height = 20
    '    .Font.Size = 9
    'End With
    
    ' ===========================右侧主编辑区域===========================
    ' 主编辑区域文本框
    Set txtContentEditor = frameScript.Controls.Add("Forms.TextBox.1", "txtContentEditor")
    With txtContentEditor
        .Top = 5
        .Left = 150
        .Width = 485
        .Height = 383
        .Font.Name = "微软雅黑"
        .Font.Size = 9

        .ScrollBars = fmScrollBarsBoth       '滚动条(根据需要)
        .BackColor = &HFFFFFF

        .MultiLine = True                           '允许多行输入，是换行的基础条件
        .EnterKeyBehavior = True               '使回车键在文本框内生成换行符（vbCrLf），而非切换焦点
        .TabKeyBehavior = True                 '允许按Tab键输入制表符而非跳转焦点
        .WordWrap = False                        ' 启用自动换行
        '.IntegralHeight = True                   ' 避免行截断
    End With
    
    ' 脚本分析标签
    Dim lblAnalysis As MSForms.Label
    Set lblAnalysis = Me.Controls.Add("Forms.Label.1", "lblAnalysis")
    With lblAnalysis
        .Caption = "脚本分析/脚本分"
        .Top = 430
        .Left = 400
        .Width = 380
        .Height = 20
        .Font.Size = 9
    End With
    
    ' === 底部查询操作区域 ===
    ' 查询操作框架
    Dim frameSearch As MSForms.Frame
    Set frameSearch = Me.Controls.Add("Forms.Frame.1", "frameSearch")
    With frameSearch
        .Caption = "查询操作"
        .Top = 450
        .Left = 10
        .Width = 380
        .Height = 100
        .Font.Size = 9
        .BackColor = &HC0C0C0
    End With
    
    ' 条件标签
    Dim lblCondition As MSForms.Label
    Set lblCondition = frameSearch.Controls.Add("Forms.Label.1", "lblCondition")
    With lblCondition
        .Caption = "条件："
        .Top = 20
        .Left = 10
        .Width = 40
        .Height = 20
        .Font.Size = 9
    End With
    
    ' 条件类型下拉框
    Dim cmbConditionType As MSForms.ComboBox
    Set cmbConditionType = frameSearch.Controls.Add("Forms.ComboBox.1", "cmbConditionType")
    With cmbConditionType
        .Top = 20
        .Left = 50
        .Width = 100
        .Height = 20
        .AddItem "MSG编号"
        .AddItem "脚本名称"
        .AddItem "作者"
        .AddItem "创建日期"
        .Value = "MSG编号"
        .Font.Size = 9
        .BackColor = &HFFFFFF
    End With
    
    ' 等于标签
    Dim lblEquals As MSForms.Label
    Set lblEquals = frameSearch.Controls.Add("Forms.Label.1", "lblEquals")
    With lblEquals
        .Caption = "="
        .Top = 20
        .Left = 160
        .Width = 20
        .Height = 20
        .TextAlign = fmTextAlignCenter
        .Font.Size = 9
    End With
    
    ' 值输入框
    Dim txtValue As MSForms.TextBox
    Set txtValue = frameSearch.Controls.Add("Forms.TextBox.1", "txtValue")
    With txtValue
        .Top = 20
        .Left = 180
        .Width = 100
        .Height = 20
        .Font.Size = 9
        .BackColor = &HFFFFFF
    End With
    
    ' 查询按钮
    Dim btnQuery As MSForms.CommandButton
    Set btnQuery = frameSearch.Controls.Add("Forms.CommandButton.1", "btnQuery")
    With btnQuery
        .Caption = "查询"
        .Top = 20
        .Left = 290
        .Width = 60
        .Height = 20
        .Font.Size = 9
        .BackColor = &HE0E0E0
    End With
    
    ' 修改按钮
    Dim btnModify As MSForms.CommandButton
    Set btnModify = frameSearch.Controls.Add("Forms.CommandButton.1", "btnModify")
    With btnModify
        .Caption = "修改"
        .Top = 60
        .Left = 100
        .Width = 60
        .Height = 24
        .Font.Size = 9
        .BackColor = &HE0E0E0
    End With
    
    ' 追加按钮
    Dim btnAppend As MSForms.CommandButton
    Set btnAppend = frameSearch.Controls.Add("Forms.CommandButton.1", "btnAppend")
    With btnAppend
        .Caption = "追加"
        .Top = 60
        .Left = 180
        .Width = 60
        .Height = 24
        .Font.Size = 9
        .BackColor = &HE0E0E0
    End With
    
    ' === 底部操作按钮 ===

End Sub

' 应用图片中的示例内容 - 修复版
Private Sub ApplySampleContent()
    ' 确保控件已创建后再访问
    On Error Resume Next
    txtFilePath.Text = "H:\360MoveData\Users\zzt\Desktop\ddml\data\npc\WM_aoki_3032.txt"

    Dim Content As String
    Content = "#NPC--暗10" & vbCrLf & _
              "#絡矢机、井矢机の惰侍はない" & vbCrLf & _
              "#_は粕み若ぼし" & vbCrLf & _
              "######## メッセ【ジウィンドウ】億く ########" & vbCrLf & _
              "Window 60271,OK"
    
    txtContentEditor.Text = Content
    On Error GoTo 0
End Sub

' 关闭按钮点击事件
Private Sub btnClose_Click()
    frameScript.Visible = False
End Sub

' ===================== 公共方法 =====================
' 设置内容编辑区文本
Public Sub SetContent(ByVal Content As String)
    txtContentEditor.Text = Content
    If Len(mFilePath) > 0 Then
        txtFilePath.Text = mFilePath
    End If
End Sub

' 公共属性：用于定义只读属性的语法结构，其核心功能是允许外部代码安全地获取类模块或用户窗体中的私有数据
Public Property Get FilePath() As String
    FilePath = mFilePath
End Property

Public Property Let FilePath(ByVal Value As String)
    mFilePath = Value
End Property

' ===================== 鼠标滚轮支持 =====================
' 文本框鼠标进入事件
Private Sub txtContentEditor_MouseMove(ByVal Button As Integer, ByVal Shift As Integer, ByVal x As Single, ByVal y As Single)
    ' 当鼠标在文本框上时，设置焦点以便接收滚轮事件
    If Not txtContentEditor.Focused Then
        txtContentEditor.SetFocus
    End If
End Sub

' 窗体鼠标滚轮事件处理
Private Sub UserForm_MouseWheel(ByVal Page As MSForms.fmScrollAction, ByVal Count As Long, ByVal Delta As Single)
    ' 检查鼠标是否在txtContentEditor上
    Dim pt As POINTAPI
    GetCursorPos pt

    ' 转换为窗体坐标
    Dim hwnd As Long
    hwnd = WindowFromPoint(pt.x, pt.y)

    ' 如果鼠标在文本编辑器区域，处理滚轮事件
    If IsMouseOverTextEditor() Then
        HandleTextEditorScroll Delta
    End If
End Sub

' 检查鼠标是否在文本编辑器上
Private Function IsMouseOverTextEditor() As Boolean
    On Error Resume Next
    Dim pt As POINTAPI
    GetCursorPos pt

    ' 简化的检查：如果文本编辑器有焦点，则认为鼠标在其上
    IsMouseOverTextEditor = (Me.ActiveControl Is txtContentEditor)
    On Error GoTo 0
End Function

' 处理文本编辑器滚动
Private Sub HandleTextEditorScroll(ByVal Delta As Single)
    On Error Resume Next

    ' 获取当前滚动位置
    Dim currentTopIndex As Long
    currentTopIndex = txtContentEditor.TopIndex

    ' 根据滚轮方向调整滚动
    If Delta > 0 Then
        ' 向上滚动
        If currentTopIndex > 0 Then
            txtContentEditor.TopIndex = currentTopIndex - 3
        End If
    Else
        ' 向下滚动
        txtContentEditor.TopIndex = currentTopIndex + 3
    End If

    On Error GoTo 0
End Sub

' 文本框获得焦点事件
Private Sub txtContentEditor_Enter()
    ' 文本框获得焦点时的处理
End Sub

' 文本框失去焦点事件
Private Sub txtContentEditor_Exit(ByVal Cancel As MSForms.ReturnBoolean)
    ' 文本框失去焦点时的处理
End Sub
