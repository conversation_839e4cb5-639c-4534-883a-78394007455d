Attribute VB_Name = "MouseWheelSupport"
Option Explicit

' Windows API声明
Private Declare PtrSafe Function CallWindowProc Lib "user32" Alias "CallWindowProcA" (ByVal lpPrevWndFunc As LongPtr, ByVal hwnd As LongPtr, ByVal Msg <PERSON> Long, ByVal wParam As LongPtr, ByVal lParam As LongPtr) As LongPtr
Private Declare PtrSafe Function SetWindowLong Lib "user32" Alias "SetWindowLongA" (ByVal hwnd As LongPtr, ByVal nIndex As Long, ByVal dwNewLong As LongPtr) As LongPtr
Private Declare PtrSafe Function FindWindow Lib "user32" Alias "FindWindowA" (ByVal lpClassName As String, ByVal lpWindowName As String) As LongPtr

' 常量
Private Const GWL_WNDPROC = -4
Private Const WM_MOUSEWHEEL = &H20A

' 全局变量
Public OldWndProc As LongPtr
Public FormHwnd As LongPtr
Public TargetForm As Object

' 窗口过程处理函数
Public Function WindowProc(ByVal hwnd As LongPtr, ByVal uMsg As Long, ByVal wParam As LongPtr, ByVal lParam As LongPtr) As LongPtr
    If uMsg = WM_MOUSEWHEEL Then
        ' 处理鼠标滚轮消息
        HandleMouseWheel wParam
        WindowProc = 0
    Else
        ' 调用原始窗口过程
        WindowProc = CallWindowProc(OldWndProc, hwnd, uMsg, wParam, lParam)
    End If
End Function

' 处理鼠标滚轮
Private Sub HandleMouseWheel(ByVal wParam As LongPtr)
    On Error Resume Next
    
    If Not TargetForm Is Nothing Then
        ' 检查当前活动控件是否为文本编辑器
        If TypeName(TargetForm.ActiveControl) = "TextBox" And TargetForm.ActiveControl.Name = "txtContentEditor" Then
            ' 提取滚轮方向 (高位字节)
            Dim wheelDelta As Integer
            wheelDelta = (wParam And &HFFFF0000) \ &H10000
            
            ' 获取当前滚动位置
            Dim currentTopIndex As Long
            currentTopIndex = TargetForm.ActiveControl.TopIndex
            
            ' 根据滚轮方向调整滚动
            If wheelDelta > 0 Then
                ' 向上滚动
                If currentTopIndex > 0 Then
                    TargetForm.ActiveControl.TopIndex = WorksheetFunction.Max(0, currentTopIndex - 3)
                End If
            Else
                ' 向下滚动
                TargetForm.ActiveControl.TopIndex = currentTopIndex + 3
            End If
        End If
    End If
    
    On Error GoTo 0
End Sub

' 设置鼠标滚轮支持
Public Sub SetupMouseWheelForForm(ByRef frm As Object)
    On Error Resume Next
    Set TargetForm = frm
    ' 获取窗体句柄并设置窗口过程
    FormHwnd = FindWindow(vbNullString, frm.Caption)
    If FormHwnd <> 0 Then
        OldWndProc = SetWindowLong(FormHwnd, GWL_WNDPROC, AddressOf WindowProc)
    End If
    On Error GoTo 0
End Sub

' 恢复原始窗口过程
Public Sub RestoreWindowProc()
    On Error Resume Next
    If FormHwnd <> 0 And OldWndProc <> 0 Then
        SetWindowLong FormHwnd, GWL_WNDPROC, OldWndProc
        FormHwnd = 0
        OldWndProc = 0
        Set TargetForm = Nothing
    End If
    On Error GoTo 0
End Sub
